# 실시간 크롤링 모니터링 API 가이드

## 개요
이 가이드는 10초 간격 폴링 방식으로 크롤링 진행률을 실시간 모니터링하는 방법을 설명합니다.

## API 엔드포인트

### 1. 전체 작업 실시간 현황 조회
```
GET /api/images/status
```

**응답 예시:**
```json
{
  "code": 200,
  "message": "실시간 수집 현황 조회 성공",
  "timestamp": 1704067200000,
  "data": [
    {
      "job_id": "550e8400-e29b-41d4-a716-446655440000",
      "keyword": "겨울",
      "source": "youtube",
      "start_date": "2025-01-01T10:00:00",
      "end_date": null,
      "target_count": 10,
      "collected_count": 7,
      "processing_count": "7/10",
      "status": "수집 중",
      "progress": 70.0,
      "file_path": "/path/to/files",
      "estimated_remaining": "3분 예상"
    },
    {
      "job_id": "550e8400-e29b-41d4-a716-446655440000",
      "keyword": "남해",
      "source": "google",
      "start_date": "2025-01-01T10:00:00",
      "end_date": "2025-01-01T10:05:00",
      "target_count": 10,
      "collected_count": 10,
      "processing_count": "10/10",
      "status": "완료",
      "progress": 100.0,
      "file_path": "/path/to/files",
      "completion_time": "2025-01-01T10:05:00"
    }
  ]
}
```

### 2. 특정 작업 실시간 현황 조회
```
POST /api/images/countJobId?jobId={job_id}
```

### 3. 간단한 진행률 조회 (권장)
```
GET /api/images/progress/{job_id}
```

**응답 예시:**
```json
{
  "job_id": "550e8400-e29b-41d4-a716-446655440000",
  "timestamp": 1704067200000,
  "total_target": 30,
  "total_collected": 17,
  "overall_progress": 56.67,
  "is_completed": false,
  "has_failed": false,
  "details": [
    {
      "source": "youtube",
      "keyword": "겨울",
      "target_count": 10,
      "collected_count": 7,
      "progress": 70.0,
      "status": "수집 중"
    },
    {
      "source": "google", 
      "keyword": "남해",
      "target_count": 10,
      "collected_count": 10,
      "progress": 100.0,
      "status": "완료"
    },
    {
      "source": "unsplash",
      "keyword": "한국",
      "target_count": 10,
      "collected_count": 0,
      "progress": 0.0,
      "status": "수집 중"
    }
  ]
}
```

## JavaScript 구현 예시

### 1. 기본 폴링 구현
```javascript
class CrawlingMonitor {
    constructor(jobId) {
        this.jobId = jobId;
        this.intervalId = null;
        this.isMonitoring = false;
    }

    // 10초 간격 모니터링 시작
    startMonitoring() {
        if (this.isMonitoring) return;
        
        this.isMonitoring = true;
        this.checkProgress(); // 즉시 한 번 실행
        
        this.intervalId = setInterval(() => {
            this.checkProgress();
        }, 10000); // 10초 간격
    }

    // 모니터링 중지
    stopMonitoring() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
        this.isMonitoring = false;
    }

    // 진행률 확인
    async checkProgress() {
        try {
            const response = await fetch(`/api/images/progress/${this.jobId}`);
            const data = await response.json();
            
            if (response.ok) {
                this.updateUI(data);
                
                // 완료 또는 실패 시 모니터링 중지
                if (data.is_completed || data.has_failed) {
                    this.stopMonitoring();
                    this.onComplete(data);
                }
            } else {
                console.error('진행률 조회 실패:', data.error);
            }
        } catch (error) {
            console.error('API 호출 오류:', error);
        }
    }

    // UI 업데이트
    updateUI(data) {
        // 전체 진행률 업데이트
        const progressBar = document.getElementById('overall-progress');
        if (progressBar) {
            progressBar.style.width = `${data.overall_progress}%`;
            progressBar.textContent = `${data.overall_progress.toFixed(1)}%`;
        }

        // 수집 현황 업데이트
        const statusElement = document.getElementById('collection-status');
        if (statusElement) {
            statusElement.textContent = `${data.total_collected}/${data.total_target} 수집 완료`;
        }

        // 개별 작업 진행률 업데이트
        data.details.forEach(item => {
            const itemElement = document.getElementById(`item-${item.source}-${item.keyword}`);
            if (itemElement) {
                const progressElement = itemElement.querySelector('.progress');
                const statusElement = itemElement.querySelector('.status');
                
                if (progressElement) {
                    progressElement.style.width = `${item.progress}%`;
                }
                if (statusElement) {
                    statusElement.textContent = item.status;
                    statusElement.className = `status ${item.status}`;
                }
            }
        });
    }

    // 완료 콜백
    onComplete(data) {
        if (data.is_completed) {
            alert('모든 이미지 수집이 완료되었습니다!');
        } else if (data.has_failed) {
            alert('일부 작업이 실패했습니다.');
        }
    }
}

// 사용 예시
const monitor = new CrawlingMonitor('your-job-id-here');
monitor.startMonitoring();

// 페이지 종료 시 모니터링 중지
window.addEventListener('beforeunload', () => {
    monitor.stopMonitoring();
});
```

### 2. React Hook 구현 예시
```javascript
import { useState, useEffect, useCallback } from 'react';

export const useCrawlingMonitor = (jobId) => {
    const [progress, setProgress] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);
    const [isCompleted, setIsCompleted] = useState(false);

    const fetchProgress = useCallback(async () => {
        if (!jobId) return;
        
        setIsLoading(true);
        setError(null);
        
        try {
            const response = await fetch(`/api/images/progress/${jobId}`);
            const data = await response.json();
            
            if (response.ok) {
                setProgress(data);
                setIsCompleted(data.is_completed || data.has_failed);
            } else {
                setError(data.error || '진행률 조회 실패');
            }
        } catch (err) {
            setError('API 호출 오류: ' + err.message);
        } finally {
            setIsLoading(false);
        }
    }, [jobId]);

    useEffect(() => {
        if (!jobId || isCompleted) return;

        // 즉시 한 번 실행
        fetchProgress();

        // 10초 간격으로 실행
        const interval = setInterval(fetchProgress, 10000);

        return () => clearInterval(interval);
    }, [jobId, isCompleted, fetchProgress]);

    return { progress, isLoading, error, isCompleted, refetch: fetchProgress };
};

// 컴포넌트에서 사용
function CrawlingProgress({ jobId }) {
    const { progress, isLoading, error, isCompleted } = useCrawlingMonitor(jobId);

    if (error) {
        return <div className="error">오류: {error}</div>;
    }

    if (!progress) {
        return <div>로딩 중...</div>;
    }

    return (
        <div className="crawling-progress">
            <h3>수집 진행률</h3>
            <div className="overall-progress">
                <div className="progress-bar">
                    <div 
                        className="progress-fill" 
                        style={{ width: `${progress.overall_progress}%` }}
                    >
                        {progress.overall_progress.toFixed(1)}%
                    </div>
                </div>
                <p>{progress.total_collected}/{progress.total_target} 수집 완료</p>
            </div>
            
            <div className="details">
                {progress.details.map((item, index) => (
                    <div key={index} className="detail-item">
                        <span>{item.source} - {item.keyword}</span>
                        <span>{item.collected_count}/{item.target_count}</span>
                        <span className={`status ${item.status}`}>{item.status}</span>
                    </div>
                ))}
            </div>
            
            {isCompleted && (
                <div className="completion-message">
                    {progress.is_completed ? '✅ 수집 완료!' : '❌ 일부 실패'}
                </div>
            )}
        </div>
    );
}
```

## 주요 특징

1. **10초 간격 폴링**: 서버 부하를 최소화하면서 실시간성 확보
2. **자동 중지**: 작업 완료 또는 실패 시 자동으로 폴링 중지
3. **오류 처리**: 네트워크 오류 및 API 오류에 대한 적절한 처리
4. **진행률 계산**: 전체 진행률과 개별 작업 진행률 제공
5. **상태 관리**: 수집 중, 완료, 실패 등 다양한 상태 지원

## 권장사항

1. **적절한 간격**: 10초 간격이 적절하며, 더 짧은 간격은 서버 부하 증가
2. **오류 처리**: 네트워크 오류 시 재시도 로직 구현
3. **사용자 경험**: 로딩 상태와 오류 상태에 대한 적절한 UI 제공
4. **리소스 관리**: 페이지 이동 시 폴링 중지로 리소스 절약
