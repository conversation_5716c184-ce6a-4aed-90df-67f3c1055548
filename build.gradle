plugins {
  id 'java'
  id 'org.springframework.boot' version '3.0.2'
  id 'io.spring.dependency-management' version '1.1.0'
}

group = 'kr.co.digitalzone'
version = '0.0.1-SNAPSHOT'
sourceCompatibility = '17'

configurations {
  compileOnly {
    extendsFrom annotationProcessor
  }
}

repositories {
  mavenCentral()
  maven { url 'https://artifactory-oss.prod.netflix.net/artifactory/maven-oss-candidates' }
}

ext {
  set('springCloudVersion', "2022.0.1")
}

dependencies {
  annotationProcessor 'org.projectlombok:lombok'
//  annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'

  compileOnly 'org.projectlombok:lombok'

  implementation 'org.springframework.boot:spring-boot-starter-web'

  // sql 변수 값 확인
  implementation 'com.github.gavlyukovskiy:p6spy-spring-boot-starter:1.5.6'

  //swagger
  implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.1.0'

  implementation('com.googlecode.json-simple:json-simple:1.1')

  // JUnit 5 dependency
//  testImplementation 'org.junit.jupiter:junit-jupiter:5.8.1'

//  developmentOnly 'org.springframework.boot:spring-boot-devtools'
//  implementation 'org.springframework.boot:spring-boot-devtools'
  implementation 'org.springframework.boot:spring-boot-starter-actuator'
  implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
  implementation 'org.springframework.boot:spring-boot-starter-security'
  implementation 'org.springframework.boot:spring-boot-starter-validation'
  implementation 'org.springframework.boot:spring-boot-starter-web'

  implementation 'org.springframework.security:spring-security-data'

  implementation 'com.github.gavlyukovskiy:p6spy-spring-boot-starter:1.9.0'
  implementation 'com.zaxxer:HikariCP:3.2.0'
  implementation 'javax.xml.bind:jaxb-api:2.3.1'
  implementation 'joda-time:joda-time:2.12.2'
  implementation 'io.jsonwebtoken:jjwt-api:0.11.5'
  implementation 'io.jsonwebtoken:jjwt-impl:0.11.5'
  implementation 'io.jsonwebtoken:jjwt-jackson:0.11.5'
  implementation 'org.javassist:javassist:3.29.2-GA'
  implementation 'org.modelmapper:modelmapper:3.1.1'
  implementation 'org.postgresql:postgresql:42.5.4'

  implementation 'io.projectreactor:reactor-core:3.1.6.RELEASE'

  testImplementation 'org.springframework.boot:spring-boot-starter-test'

  implementation 'org.python:jython-slim:2.7.3rc1' //jython

  // Querydsl 추가
  implementation 'com.querydsl:querydsl-jpa:5.0.0:jakarta'
  annotationProcessor 'com.querydsl:querydsl-apt:5.0.0:jakarta'
  annotationProcessor "com.querydsl:querydsl-apt:${dependencyManagement.importedProperties['querydsl.version']}:jakarta"
  annotationProcessor "jakarta.annotation:jakarta.annotation-api"
  annotationProcessor "jakarta.persistence:jakarta.persistence-api"

  // File
  implementation 'commons-io:commons-io:2.5'    /* Apache commons-io */
  implementation group: 'commons-fileupload', name: 'commons-fileupload', version: '1.3.3'
  /* Apache Commons FileUpload */
  implementation 'com.googlecode.json-simple:json-simple:1.1'

  // Gson
  implementation 'com.google.code.gson:gson:2.8.7'

  // Aop
  implementation 'org.springframework.boot:spring-boot-starter-aop'

  // Validation
  implementation 'org.springframework.boot:spring-boot-starter-validation'

  // Mail
  implementation 'org.springframework.boot:spring-boot-starter-mail'

  // jwt
  implementation 'io.jsonwebtoken:jjwt:0.9.1'
  implementation 'javax.xml.bind:jaxb-api:2.3.1'
//  runtime 'io.jsonwebtoken:jjwt-impl:0.9.1'
//  runtime 'io.jsonwebtoken:jjwt-jackson:0.9.1'
  testImplementation 'org.springframework.security:spring-security-test'
}

// Qclass 생성 위치 설정
sourceSets {
  main {
    java {
      srcDirs = ['src/main/java', 'src/main/generated'] // 추가
    }
  }
}

compileJava {
  options.annotationProcessorPath = configurations.annotationProcessor
}

// clean 시 generated 폴더도 삭제
clean {
  delete file('src/main/generated')
}

dependencyManagement {
  imports {
    mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
  }
}

tasks.named('test') {
  useJUnitPlatform()
}
