import datetime
import time
import os
import urllib.request
from selenium import webdriver
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from pymongo import MongoClient
import sys
import urllib.request
from selenium import webdriver
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.by import By
from pymongo import MongoClient
from selenium.webdriver.common.desired_capabilities import DesiredCapabilities
from fake_useragent import UserAgent


# UserAgent 객체를 미리 생성
ua = UserAgent()
def get_chrome_driver():
    try:
        options = Options()
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--remote-debugging-port=9222")
        options.add_argument('--disable-blink-features=AutomationControlled')
        # Synology NAS용 Chrome 경로로 변경
        # options.binary_location = '/usr/bin/google-chrome'
        options.binary_location = '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome'

        # fake-useragent 라이브러리를 사용하여 User-Agent 설정
        user_agent = ua.random
        options.add_argument(f'user-agent={user_agent}')

        # ChromeDriver 설치 및 서비스 설정
        # driver_path = ChromeDriverManager(driver_version="131.0.6778.86").install()
        # service = Service(driver_path)
        driver = webdriver.Chrome(
            service=Service(ChromeDriverManager().install()), # 가장 많이 바뀐 부분
            options=chrome_options)



    # driver = webdriver.Chrome(service=service, options=options)
        print("ChromeDriver가 성공적으로 설정되었습니다.")
        return driver
    except Exception as e:
        print(f"ChromeDriver 설정 중 오류 발생: {str(e)}")
        return None



def get_remote_chrome_driver():
    try:
        options = webdriver.ChromeOptions()
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument("--headless")  # 필요한 경우 headless 모드 활성화

        # Remote WebDriver 설정
        driver = webdriver.Remote(
            command_executor='http://211.232.75.159:4444',  # Docker 컨테이너와 연결
            options=options
        )
        print("Remote ChromeDriver가 성공적으로 설정되었습니다.")
        return driver
    except Exception as e:
        print(f"Remote ChromeDriver 설정 중 오류 발생: {str(e)}")
        return None





# def collect_images(keyword: str, collect_count: int, save_dir='/volume1/pj1/datalake/image'):
def collect_images(keyword: str, collect_count: int, save_dir='/Users/<USER>/rd/labeling/crawling'):
    # keyword = sys.argv[1]
    # collect_count = int(sys.argv[2])
    keyword_dir = os.path.join(save_dir, keyword)
    if not os.path.exists(keyword_dir):
        os.makedirs(keyword_dir)

    ## 기존 이미지 파일 수를 확인하여 새로운 파일 이름 설정
    existing_files = os.listdir(keyword_dir)
    collected_count = len(existing_files)

    ## 총 수집할 이미지 개수 설정
    total_collect_count = collected_count + collect_count

    # ## 수집된 이미지 개수
    # collectedImages = 0

# driver = get_remote_chrome_driver()
    driver=get_chrome_driver()
    if driver is None:
        print('드라이버 초기화 실패')
        return

    try:
        driver.get("https://www.google.co.kr/imghp?hl=ko")
        time.sleep(5)

        input_box = driver.find_element("name", 'q')
        input_box.send_keys(keyword + Keys.ENTER)
        time.sleep(2)

        client = MongoClient('**********************************************************', 27017)
        db = client['db_metadata']
        collection = db['image_metadata']

        ## 기존 수집된 개수부터 시작하여 총 수집할 개수까지 반복
        while collected_count < total_collect_count:
            div = driver.find_element(By.CLASS_NAME, 'wIjY0d.jFk0f')
            images = div.find_elements(By.CSS_SELECTOR, 'div.H8Rx8c > g-img > img')
            sources = driver.find_elements(By.CSS_SELECTOR, 'div.juwGPd.BwPElf.OCzgxd > a')

            for img, source in zip(images, sources):
                if collected_count >= total_collect_count:
                    break
                time.sleep(2)
                url = img.get_attribute('src')
                title = source.get_attribute('href')
                # print(f'{collected_count+1} 번째 수집완료')
                ## 새로운 파일 이름 설정
                filename = os.path.join(keyword_dir, f'{keyword}_{collected_count}.jpg')
                urllib.request.urlretrieve(url, filename)

                metadata = {
                    "_id": f'{keyword}_{collected_count}_{int(time.time())}',  # 고유한 _id 생성
                    "keyword": keyword,
                    "source": title,
                    "file_path": url,
                    "registered_at": datetime.datetime.now(),
                    "size_byte": f'{os.path.getsize(filename)} bytes',
                    "file_name": filename
                }

                collection.insert_one(metadata)

                collected_count += 1
                # collectedImages += 1  # collectedImages 증가
                print(f'현재까지 수집된 이미지 수: {collected_count}개 완료')  # 수집된 이미지 수 출력
                time.sleep(2)

            driver.find_element(By.TAG_NAME, 'body').send_keys(Keys.END)
            time.sleep(2)

    finally:
        driver.quit()  # Remote WebDriver는 quit()로 종료
        print('이미지 수집을 종료합니다')

if __name__ == "__main__":
    keyword = sys.argv[1]
    collect_count = int(sys.argv[2])

    # 로컬
    # keyword = input("keyword: ")
    # collect_count = int(input("count: "))
    print(f"Keyword: {keyword}, Collection Count: {collect_count}")
    collect_images(keyword, collect_count)


