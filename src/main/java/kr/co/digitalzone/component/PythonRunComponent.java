package kr.co.digitalzone.component;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class PythonRunComponent {

    @Value("${python.script.path:/Users/<USER>/rd/labelingService/labelingBackend/crawling.py}")
    private String pythonScriptPath;

    @Value("${python.venv.path:/Users/<USER>/rd/labelingService/labelingBackend/labeling/bin/activate}")
    private String virtualEnvPath;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * crawling.py 스크립트를 실행하는 메서드
     * @param jsonData 프론트엔드에서 받은 JSON 데이터 리스트
     * @return 실행 결과 ("success", "fail", "error")
     */
    public String runCrawlingPython(List<Map<String, Object>> jsonData) {
        log.info("Python 크롤링 스크립트 실행 시작 - 데이터 개수: {}", jsonData.size());

        try {
            // JSON 데이터를 문자열로 변환
            String jsonString = objectMapper.writeValueAsString(jsonData);
            log.info("전달할 JSON 데이터: {}", jsonString);

            // 운영체제 확인
            boolean isWindows = System.getProperty("os.name").toLowerCase().startsWith("windows");
            
            // Python 실행 명령어 구성
            ProcessBuilder builder = new ProcessBuilder();
            
            if (isWindows) {
                // Windows 환경
                builder.command("cmd", "/c", 
                    String.format("python \"%s\" \"%s\"", pythonScriptPath, jsonString));
            } else {
                // Unix/Linux/Mac 환경 - 가상환경 활성화 후 실행
                String command = String.format(
                    "source %s && python %s '%s'", 
                    virtualEnvPath, pythonScriptPath, jsonString
                );
                builder.command("bash", "-c", command);
            }

            // 작업 디렉토리 설정
            File workingDir = new File(System.getProperty("user.home"));
            builder.directory(workingDir);
            
            // 에러 스트림을 표준 출력으로 리다이렉트
            builder.redirectErrorStream(true);

            log.info("Python 명령어 실행: {}", builder.command());
            
            // 프로세스 시작
            Process process = builder.start();

            // 프로세스 출력 읽기
            try (BufferedReader br = new BufferedReader(
                    new InputStreamReader(process.getInputStream(), "utf-8"))) {
                
                String line;
                while ((line = br.readLine()) != null) {
                    log.info("Python 출력: {}", line);
                }
            }

            // 프로세스 완료 대기
            int exitCode = process.waitFor();
            log.info("Python 스크립트 실행 완료 - 종료 코드: {}", exitCode);

            if (exitCode == 0) {
                log.info("Python 크롤링 스크립트 실행 성공");
                return "success";
            } else {
                log.error("Python 크롤링 스크립트 실행 실패 - 종료 코드: {}", exitCode);
                return "fail";
            }

        } catch (Exception e) {
            log.error("Python 크롤링 스크립트 실행 중 오류 발생", e);
            return "error";
        }
    }

    /**
     * 비동기로 crawling.py 스크립트를 실행하는 메서드
     * @param jsonData 프론트엔드에서 받은 JSON 데이터 리스트
     * @return 프로세스 시작 성공 여부 ("started", "fail", "error")
     */
    public String runCrawlingPythonAsync(List<Map<String, Object>> jsonData) {
        log.info("Python 크롤링 스크립트 비동기 실행 시작 - 데이터 개수: {}", jsonData.size());

        try {
            // JSON 데이터를 문자열로 변환
            String jsonString = objectMapper.writeValueAsString(jsonData);
            log.info("전달할 JSON 데이터: {}", jsonString);

            // 운영체제 확인
            boolean isWindows = System.getProperty("os.name").toLowerCase().startsWith("windows");
            
            // Python 실행 명령어 구성
            ProcessBuilder builder = new ProcessBuilder();
            
            if (isWindows) {
                // Windows 환경
                builder.command("cmd", "/c", 
                    String.format("python \"%s\" \"%s\"", pythonScriptPath, jsonString));
            } else {
                // Unix/Linux/Mac 환경 - 가상환경 활성화 후 실행
                String command = String.format(
                    "source %s && python %s '%s'", 
                    virtualEnvPath, pythonScriptPath, jsonString
                );
                builder.command("bash", "-c", command);
            }

            // 작업 디렉토리 설정
            File workingDir = new File(System.getProperty("user.home"));
            builder.directory(workingDir);
            
            // 에러 스트림을 표준 출력으로 리다이렉트
            builder.redirectErrorStream(true);

            log.info("Python 명령어 실행: {}", builder.command());
            
            // 프로세스 시작
            Process process = builder.start();

            // 프로세스가 정상적으로 시작되었는지 확인 (짧은 시간 대기)
            Thread.sleep(1000);
            
            if (process.isAlive()) {
                log.info("Python 크롤링 스크립트 비동기 실행 시작 성공");
                
                // 백그라운드에서 프로세스 모니터링 (예외 처리 강화)
                new Thread(() -> {
                    try (BufferedReader br = new BufferedReader(
                            new InputStreamReader(process.getInputStream(), "utf-8"))) {

                        String line;
                        while ((line = br.readLine()) != null) {
                            log.info("Python 출력: {}", line);
                        }

                        int exitCode = process.waitFor();
                        if (exitCode == 0) {
                            log.info("Python 크롤링 스크립트 백그라운드 실행 완료 - 성공");
                        } else {
                            log.error("Python 크롤링 스크립트 백그라운드 실행 완료 - 실패 (종료 코드: {})", exitCode);
                        }

                    } catch (InterruptedException e) {
                        log.warn("Python 프로세스 모니터링이 중단되었습니다.", e);
                        Thread.currentThread().interrupt();
                    } catch (IOException e) {
                        log.warn("Python 프로세스 출력 읽기 중 I/O 오류: {}", e.getMessage());
                    } catch (Exception e) {
                        log.error("Python 크롤링 스크립트 백그라운드 모니터링 중 예상치 못한 오류", e);
                    }
                }, "python-monitor-thread").start();
                
                return "started";
            } else {
                log.error("Python 크롤링 스크립트 프로세스가 즉시 종료됨");
                return "fail";
            }

        } catch (Exception e) {
            log.error("Python 크롤링 스크립트 비동기 실행 중 오류 발생", e);
            return "error";
        }
    }

    /**
     * Python 스크립트 파일 존재 여부 확인
     */
    public boolean checkPythonScriptExists() {
        Path scriptPath = Paths.get(pythonScriptPath);
        boolean exists = Files.exists(scriptPath);
        log.info("Python 스크립트 파일 존재 여부: {} - {}", exists, pythonScriptPath);
        return exists;
    }

    /**
     * 가상환경 활성화 스크립트 존재 여부 확인
     */
    public boolean checkVirtualEnvExists() {
        Path venvPath = Paths.get(virtualEnvPath);
        boolean exists = Files.exists(venvPath);
        log.info("가상환경 활성화 스크립트 존재 여부: {} - {}", exists, virtualEnvPath);
        return exists;
    }

    /**
     * crawling.py 스크립트를 실행하고 출력을 반환하는 메서드
     * @param jsonData 프론트엔드에서 받은 JSON 데이터 리스트
     * @return 실행 결과와 출력을 포함한 결과 객체
     */
    public PythonExecutionResult runCrawlingPythonWithOutput(List<Map<String, Object>> jsonData) {
        log.info("Python 크롤링 스크립트 실행 시작 (출력 포함) - 데이터 개수: {}", jsonData.size());

        try {
            // JSON 데이터를 문자열로 변환
            String jsonString = objectMapper.writeValueAsString(jsonData);
            log.info("전달할 JSON 데이터: {}", jsonString);

            // 운영체제 확인
            boolean isWindows = System.getProperty("os.name").toLowerCase().startsWith("windows");

            // Python 실행 명령어 구성
            ProcessBuilder builder = new ProcessBuilder();

            if (isWindows) {
                // Windows 환경
                builder.command("cmd", "/c",
                    String.format("python \"%s\" \"%s\"", pythonScriptPath, jsonString));
            } else {
                // Unix/Linux/Mac 환경 - 가상환경 활성화 후 실행
                String command = String.format(
                    "source %s && python %s '%s'",
                    virtualEnvPath, pythonScriptPath, jsonString
                );
                builder.command("bash", "-c", command);
            }

            // 작업 디렉토리 설정
            File workingDir = new File(System.getProperty("user.home"));
            builder.directory(workingDir);

            // 에러 스트림을 표준 출력으로 리다이렉트
            builder.redirectErrorStream(true);

            log.info("Python 명령어 실행: {}", builder.command());

            // 프로세스 시작
            Process process = builder.start();

            // 프로세스 출력 읽기
            StringBuilder output = new StringBuilder();
            try (BufferedReader br = new BufferedReader(
                    new InputStreamReader(process.getInputStream(), "utf-8"))) {

                String line;
                while ((line = br.readLine()) != null) {
                    log.info("Python 출력: {}", line);
                    output.append(line).append("\n");
                }
            }

            // 프로세스 완료 대기
            int exitCode = process.waitFor();
            log.info("Python 스크립트 실행 완료 - 종료 코드: {}", exitCode);

            String status;
            if (exitCode == 0) {
                log.info("Python 크롤링 스크립트 실행 성공");
                status = "success";
            } else {
                log.error("Python 크롤링 스크립트 실행 실패 - 종료 코드: {}", exitCode);
                status = "fail";
            }

            return new PythonExecutionResult(status, output.toString(), exitCode);

        } catch (Exception e) {
            log.error("Python 크롤링 스크립트 실행 중 오류 발생", e);
            return new PythonExecutionResult("error", "실행 중 오류 발생: " + e.getMessage(), -1);
        }
    }

    /**
     * Python 실행 결과를 담는 내부 클래스
     */
    public static class PythonExecutionResult {
        private final String status;
        private final String output;
        private final int exitCode;

        public PythonExecutionResult(String status, String output, int exitCode) {
            this.status = status;
            this.output = output;
            this.exitCode = exitCode;
        }

        public String getStatus() { return status; }
        public String getOutput() { return output; }
        public int getExitCode() { return exitCode; }
    }
}
