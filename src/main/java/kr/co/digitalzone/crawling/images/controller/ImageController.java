package kr.co.digitalzone.crawling.images.controller;


import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import kr.co.digitalzone.crawling.images.service.ImageService;
//import kr.co.digitalzone.response.ApiResponse;
//import kr.co.digitalzone.response.ApiResponseEnum;
import kr.co.digitalzone.dto.ImageCollectionReqDto;
import kr.co.digitalzone.dto.ImageCollectionResultDto;
import kr.co.digitalzone.dto.ImageCrawlingRequestDto;
import kr.co.digitalzone.dto.ImageCrawlingResponseDto;
import kr.co.digitalzone.user.common.dto.ResponseDto;
import kr.co.digitalzone.util.SecurityUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/crawling")
@Slf4j
public class ImageController {

    @Autowired
    private ImageService imageService;

//    @PostMapping("/images")
//    public String collectImages(@RequestParam String keyword, @RequestParam int collect_count) {
//        return imageService.collectImages(keyword, collect_count);
//    }
//    @PostMapping("/images")
//    public ResponseEntity<String> collectImages(@RequestParam String keyword, @RequestParam int collect_count)  {
//        String result = imageService.collectImages(keyword, collect_count);
//        return ResponseEntity.ok(result);
//    }
//    @GetMapping("/progress")
//    public ResponseEntity<Integer> getProgress() {
//        int collectedCount = imageService.getCollectedImagesCount();
//        return ResponseEntity.ok(collectedCount);
//}
//    @GetMapping("/progress")
//    public Map<String, Object> getProgress() {
//        return imageService.getProgress();
//    }
//    @GetMapping("/detail")
//    public Map<String, Object> getDetail() {
//        return imageService.getDetail();
//    }

//    /**
//     * 이미지 수집 API
//     * 요청: List<ImageCollectionRequestDto>
//     * 응답: ResponseDto<List<ImageCollectionResultDto>>
//     */
//    @PostMapping("/images")
//    public ResponseEntity<ResponseDto<List<ImageCollectionResultDto>>> collectImages(
//            @Valid @RequestBody List<ImageCollectionReqDto> requests) {
//
//        try {
//            List<ImageCollectionResultDto> results = imageService.collectImages(requests);
//
//            // 결과 분석
//            long collectingCount = results.stream()
//                    .filter(result -> "수집 중".equals(result.getStatus()))
//                    .count();
//
//            long failureCount = results.stream()
//                    .filter(result -> "실패".equals(result.getStatus()) || "오류".equals(result.getStatus()))
//                    .count();
//
//            // 응답 메시지와 상태 코드 결정
//            ResponseDto<List<ImageCollectionResultDto>> response;
//            HttpStatus httpStatus;
//
//            if (failureCount == 0 && collectingCount > 0) {
//                // 모든 요청이 수집 중인 경우 (정상 시작)
//                response = new ResponseDto<>(200, "이미지 수집 성공", results);
//                httpStatus = HttpStatus.OK;
//            } else if (collectingCount == 0) {
//                // 모든 요청이 실패한 경우
//                response = new ResponseDto<>(400, "이미지 수집 실패", results);
//                httpStatus = HttpStatus.BAD_REQUEST;
//            } else {
//                // 일부 성공, 일부 실패한 경우
//                String message = String.format("이미지 수집 부분 완료 (수집 중: %d, 실패: %d)",
//                        collectingCount, failureCount);
//                response = new ResponseDto<>(206, message, results);
//                httpStatus = HttpStatus.PARTIAL_CONTENT;
//            }
//
//            return new ResponseEntity<>(response, httpStatus);
//
//        } catch (Exception e) {
//            ResponseDto<List<ImageCollectionResultDto>> errorResponse =
//                    new ResponseDto<>(500, "이미지 수집 실패: " + e.getMessage(), null);
//
//            return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
//        }
//    }

    /**
     * 실시간 데이터 수집 현황 조회 (10초 간격 폴링용) - 사용자별 필터링
     * 대시보드에서 실시간 모니터링을 위해 사용
     * @return 실시간 수집 현황 데이터 (로그인한 사용자의 데이터만)
     */
    @GetMapping("/status")
    public Map<String, Object> getCrawlingStatus() {
        String currentUser = SecurityUtil.getCurrentUserEmailOrDefault("anonymous");
        log.info("크롤링 상태 조회 요청 - 사용자: {}", currentUser);
        return imageService.getCrawlingStatus();
    }

    /**
     * 프론트에서 선택한 job_id들의 실시간 데이터 수집 현황 조회 (10초 간격 폴링용)
     * 사용자가 프론트에서 선택한 작업들의 job_id를 파라미터로 받아서 처리
     * @param jobIds 조회할 작업 ID들 (쉼표로 구분된 문자열, 예: "job1,job2,job3")
     * @return 선택된 작업들의 실시간 수집 현황
     */
    @GetMapping("/countJobId")
    public Map<String, Object> getCrawlingCountByJobIds(@RequestParam String jobIds) {
        return imageService.getCrawlingCountByJobIds(jobIds);
    }

    /**
     * 특정 job_id의 실시간 데이터 수집 현황 조회 (10초 간격 폴링용)
     * 기존 기능 유지 - 특정 job_id를 지정해서 조회할 때 사용 (POST 방식)
     * @param jobId 조회할 작업 ID
     * @return 특정 작업의 실시간 수집 현황
     */
    @PostMapping("/countJobId")
    public Map<String, Object> getCrawlingCount(@RequestParam String jobId) {
        return imageService.getCrawlingCount(jobId);
    }

    /**
     * 임시 하드코딩
     * 전체 데이터 수집 카운트 현황 조회
     * 파이 그래프용
     * @return 전체 수집 현황
     */
    @GetMapping("/count")
    public Map<String, Object> getCrawlingCount() {
        return imageService.getCrawlingCount();
    }

//    /**
//     * 실시간 진행률 조회 API (간단 버전)
//     * 클라이언트에서 10초마다 호출하여 진행률만 확인
//     * @param jobId 조회할 작업 ID
//     * @return 간단한 진행률 정보
//     */
//    @GetMapping("/progress/{jobId}")
//    public ResponseEntity<Map<String, Object>> getProgress(@PathVariable String jobId) {
//        try {
//            Map<String, Object> result = imageService.getCrawlingCount(jobId);
//
//            // 간단한 진행률 정보만 추출
//            Map<String, Object> progressInfo = new HashMap<>();
//            progressInfo.put("job_id", jobId);
//            progressInfo.put("timestamp", System.currentTimeMillis());
//
//            if (result.get("code").equals(200)) {
//                List<Map<String, Object>> data = (List<Map<String, Object>>) result.get("data");
//
//                int totalTarget = 0;
//                long totalCollected = 0;
//                boolean allCompleted = true;
//                boolean anyFailed = false;
//
//                for (Map<String, Object> item : data) {
//                    totalTarget += (Integer) item.get("target_count");
//                    totalCollected += (Long) item.get("collected_count");
//
//                    String status = (String) item.get("status");
//                    if (!"완료".equals(status)) {
//                        allCompleted = false;
//                    }
//                    if ("실패".equals(status)) {
//                        anyFailed = true;
//                    }
//                }
//
//                double overallProgress = totalTarget > 0 ? (double) totalCollected / totalTarget * 100 : 0;
//
//                progressInfo.put("total_target", totalTarget);
//                progressInfo.put("total_collected", totalCollected);
//                progressInfo.put("overall_progress", Math.round(overallProgress * 100.0) / 100.0);
//                progressInfo.put("is_completed", allCompleted);
//                progressInfo.put("has_failed", anyFailed);
//                progressInfo.put("details", data);
//
//                return ResponseEntity.ok(progressInfo);
//            } else {
//                progressInfo.put("error", result.get("message"));
//                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(progressInfo);
//            }
//
//        } catch (Exception e) {
//            Map<String, Object> errorInfo = new HashMap<>();
//            errorInfo.put("job_id", jobId);
//            errorInfo.put("error", "진행률 조회 실패: " + e.getMessage());
//            errorInfo.put("timestamp", System.currentTimeMillis());
//
//            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorInfo);
//        }
//    }

    /**
     * 키워드별 데이터 보유 현황 조회
     *  막대그래프
     * @return
     */
    @GetMapping("/keywordCount")
    public Map<String, Object> getKeywordCount() {
        return imageService.getKeywordCount();
    }

    /**
     * 특정 jobId 데이터 삭제
     * @param jobId 삭제할 작업 ID
     * @return 삭제 결과
     */
    @DeleteMapping("/delete")
    public ResponseEntity<Map<String, Object>> deleteCrawlingData(@RequestParam String jobId) {
        try {
            Map<String, Object> result = imageService.deleteCrawlingData(jobId);

            int code = (Integer) result.get("code");
            HttpStatus httpStatus;

            switch (code) {
                case 200:
                    httpStatus = HttpStatus.OK;
                    break;
                case 404:
                    httpStatus = HttpStatus.NOT_FOUND;
                    break;
                case 500:
                default:
                    httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
                    break;
            }

            return new ResponseEntity<>(result, httpStatus);

        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", 500);
            errorResponse.put("message", "데이터 삭제 실패: " + e.getMessage());
            errorResponse.put("jobId", jobId);

            return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * PythonRunComponent를 사용한 이미지 크롤링 API (Broken pipe 오류 방지)
     * 요청: List<ImageCrawlingRequestDto>
     * 응답: ResponseDto<List<ImageCrawlingResponseDto>>
     */
    @PostMapping("/imageDown")
    public ResponseEntity<ResponseDto<List<ImageCrawlingResponseDto>>> executeImageCrawling(
            @Valid @RequestBody List<ImageCrawlingRequestDto> requests,
            HttpServletRequest httpRequest,
            HttpServletResponse httpResponse) {

        try {
            log.info("이미지 크롤링 요청 수신 - 요청 개수: {}, 클라이언트 IP: {}",
                    requests.size(), getClientIpAddress(httpRequest));

            // 클라이언트 연결 상태 확인
            if (isClientDisconnected(httpRequest)) {
                log.warn("클라이언트 연결이 이미 끊어진 상태입니다.");
                return null; // 응답하지 않음
            }

            // JWT 토큰에서 사용자 이메일 추출
            String currentUserEmail = SecurityUtil.getCurrentUserEmailOrDefault("<EMAIL>");
            log.info("크롤링 요청 사용자: {}", currentUserEmail);

            // 각 요청에 사용자 정보 설정
            for (ImageCrawlingRequestDto request : requests) {
                request.setCreated_by(currentUserEmail);
                log.info("크롤링 요청 - 소스: {}, 키워드: {}, 목표: {}, 사용자: {}",
                        request.getSource(), request.getKeyword(), request.getTarget_count(), currentUserEmail);
            }

            // 응답 헤더 설정 (연결 유지 및 타임아웃 방지)
            httpResponse.setHeader("Connection", "keep-alive");
            httpResponse.setHeader("Keep-Alive", "timeout=30, max=100");

            // ImageService를 통해 크롤링 실행 (비동기 처리)
            List<ImageCrawlingResponseDto> results = imageService.executeImageCrawling(requests);

            // 여기에 추가 - Python 스크립트 실행 후 비동기 업데이트
            List<String> jobIds = results.stream()
                    .map(ImageCrawlingResponseDto::getJob_id)
                    .collect(Collectors.toList());

            // 비동기로 created_by 업데이트
            imageService.updateTargetDataCreatedBy(jobIds, currentUserEmail);

            // 클라이언트 연결 상태 재확인
            if (isClientDisconnected(httpRequest)) {
                log.warn("응답 전송 전 클라이언트 연결이 끊어졌습니다.");
                return null; // 응답하지 않음
            }

            // 성공 응답 생성
            ResponseDto<List<ImageCrawlingResponseDto>> successResponse =
                    new ResponseDto<>(200, "이미지 크롤링 요청이 성공적으로 처리되었습니다.", results);

            log.info("이미지 크롤링 요청 처리 완료 - 응답 개수: {}", results.size());
            return new ResponseEntity<>(successResponse, HttpStatus.OK);

        } catch (Exception e) {
            log.error("이미지 크롤링 요청 처리 중 오류 발생", e);

            // 클라이언트 연결 상태 확인 후 응답
            if (isClientDisconnected(httpRequest)) {
                log.warn("오류 응답 전송 전 클라이언트 연결이 끊어졌습니다.");
                return null; // 응답하지 않음
            }

            try {
                // 실패 응답 생성
                ResponseDto<List<ImageCrawlingResponseDto>> errorResponse =
                        new ResponseDto<>(500, "이미지 크롤링 요청 처리 실패: " + e.getMessage(), null);

                return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
            } catch (Exception responseException) {
                log.error("오류 응답 생성 중 추가 오류 발생", responseException);
                return null; // 응답하지 않음
            }
        }
    }

    /**
     * 클라이언트 IP 주소 추출
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }

    /**
     * 클라이언트 연결 상태 확인
     */
    private boolean isClientDisconnected(HttpServletRequest request) {
        try {
            // 요청 스트림이 닫혔는지 확인
            request.getInputStream().available();
            return false;
        } catch (IOException e) {
            log.debug("클라이언트 연결 끊어짐 감지: {}", e.getMessage());
            return true;
        } catch (Exception e) {
            log.debug("클라이언트 연결 상태 확인 중 오류: {}", e.getMessage());
            return false; // 확실하지 않은 경우 연결된 것으로 가정
        }
    }
}



