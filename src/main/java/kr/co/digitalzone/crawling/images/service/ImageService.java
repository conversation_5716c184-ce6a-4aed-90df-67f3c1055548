package kr.co.digitalzone.crawling.images.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import kr.co.digitalzone.component.PythonRunComponent;
import kr.co.digitalzone.component.PythonRunComponent.PythonExecutionResult;
import kr.co.digitalzone.dto.CrawlingCountDto;
import kr.co.digitalzone.dto.ImageCollectionReqDto;
import kr.co.digitalzone.dto.ImageCollectionResultDto;
import kr.co.digitalzone.dto.ImageCrawlingRequestDto;
import kr.co.digitalzone.dto.ImageCrawlingResponseDto;
import kr.co.digitalzone.dto.KeywordCountDto;
import kr.co.digitalzone.entity.TargetData;
import kr.co.digitalzone.repository.ImageMetadataRepository;
import kr.co.digitalzone.repository.TargetDataRepository;
import kr.co.digitalzone.user.common.dto.ResponseDto;
import kr.co.digitalzone.util.SecurityUtil;
import lombok.Builder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
@Service
@Slf4j
@RequiredArgsConstructor
public class ImageService {
//    private static final Logger log = LoggerFactory.getLogger(ImageService.class);
//    private int collectedImagesCount = 0;

    // Repository 주입
    private final ImageMetadataRepository imageMetadataRepository;
    private final TargetDataRepository targetDataRepository;

    // PythonRunComponent 주입
    private final PythonRunComponent pythonRunComponent;

    // 스레드 풀 (동시 처리를 위한)
    private final ExecutorService executorService = Executors.newFixedThreadPool(5);

//    /**
//     * 개별 이미지 수집 처리
//     * @param request 수집 요청
//     * @return 수집 결과
//     */
//    public ImageCollectionResultDto processImageCollection(ImageCollectionReqDto request) {
//
//        log.info("이미지 수집 시작 - 소스: {}, 키워드: {}, 목표: {}",
//                request.getSource(), request.getKeyword(), request.getTarget_count());
//
//        try {
//            // Python 스크립트 실행을 위한 명령어 구성
//            String pythonScriptPath = getPythonScriptPath();
//            String imageOutputDir = getImageOutputDir();
//
//            List<String> command = buildPythonCommand(pythonScriptPath, request);
//
//            // Python 스크립트 실행
//            ProcessBuilder processBuilder = new ProcessBuilder(command);
//            Process process = processBuilder.start();
//
//            // 프로세스 출력 모니터링
//            int collectedCount = monitorProcessOutput(process, request);
//
//            // 프로세스 완료 대기
//            int exitCode = process.waitFor();
//
//            if (exitCode == 0) {
//                return ImageCollectionResultDto.builder()
//                        .source(request.getSource())
//                        .keyword(request.getKeyword())
//                        .status("수집 중")
//                        .build();
//            } else {
//                log.error("Python 스크립트 실행 실패 - 종료 코드: {}", exitCode);
//                return ImageCollectionResultDto.builder()
//                        .source(request.getSource())
//                        .keyword(request.getKeyword())
//                        .status("실패")
//                        .build();
//            }
//
//        } catch (Exception e) {
//            log.error("이미지 수집 중 오류 발생", e);
//            return ImageCollectionResultDto.builder()
//                    .source(request.getSource())
//                    .keyword(request.getKeyword())
//                    .status("오류")
//                    .build();
//        }
//    }

//    /**
//     * Python 명령어 구성 (단일 요청용) - 임시 파일 사용
//     */
//    private List<String> buildPythonCommand(String pythonScriptPath, ImageCollectionReqDto request) {
//        // 단일 요청을 리스트로 변환하여 다중 요청 메서드 재사용
//        List<ImageCollectionReqDto> requests = List.of(request);
//        return buildPythonCommand(pythonScriptPath, requests);
//    }

    /**
     * Python 명령어 구성 (다중 요청용) - 임시 파일 사용
     */
    private List<String> buildPythonCommand(String pythonScriptPath, List<ImageCollectionReqDto> requests) {
        try {
            // JSON 데이터를 문자열로 변환
            ObjectMapper objectMapper = new ObjectMapper();
            String jsonString = objectMapper.writeValueAsString(requests);
            log.info("생성된 JSON 문자열 (다중): {}", jsonString);

            // 임시 파일에 JSON 데이터 저장
            File tempFile = File.createTempFile("image_collection_", ".json");
            tempFile.deleteOnExit(); // JVM 종료 시 자동 삭제

            try (java.io.FileWriter writer = new java.io.FileWriter(tempFile)) {
                writer.write(jsonString);
            }

            log.info("임시 JSON 파일 생성: {}", tempFile.getAbsolutePath());

            // Python 명령어 구성
            List<String> command = new ArrayList<>();
            command.add("bash");
            command.add("-c");

            String pythonCommand = String.format(
                    "source %s && python %s \"$(cat %s)\"",
                    getVirtualEnvPath(),
                    pythonScriptPath,
                    tempFile.getAbsolutePath()
            );

            command.add(pythonCommand);
            log.info("최종 Python 명령어 (다중): {}", pythonCommand);
            return command;

        } catch (Exception e) {
            throw new RuntimeException("Python 명령어 구성 실패", e);
        }
    }

//    /**
//     * 프로세스 출력 모니터링 및 수집 개수 카운팅
//     */
//    private int monitorProcessOutput(Process process, ImageCollectionReqDto request) {
//        int collectedCount = 0;
//
//        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
//            String line;
//            while ((line = reader.readLine()) != null) {
//                log.info("Python 출력: {}", line);
//
//                // 수집 완료 로그 감지 (Python 스크립트의 출력 형식에 따라 조정 필요)
//                if (line.contains("이미지 다운로드 완료") || line.contains("완료")) {
//                    collectedCount++;
//                }
//
//                // 진행률 로그 등 추가 처리 가능
//                if (line.contains("진행률")) {
//                    // 진행률 정보 처리
//                }
//            }
//        } catch (Exception e) {
//            log.error("프로세스 출력 모니터링 중 오류", e);
//        }
//
//        return Math.min(collectedCount, request.getTarget_count());
//    }




    /**
     * 환경별 경로 설정 메서드들
     */
    // Python 스크립트 경로
    private String getPythonScriptPath() {
        // 로컬 환경
        return "/Users/<USER>/rd/labelingService/labelingBackend/total_crawler.py";
        // 배포 환경
//         return "/volume1/pj1/backEnd/labeler/total_crawler.py";
    }

    // 가상환경 경로
    private String getVirtualEnvPath() {
        // 로컬 환경
        return "/Users/<USER>/rd/labelingService/labelingBackend/labeling/bin/activate";
        // 배포 환경
//         return "/volume1/pj1/backEnd/labeling/bin/activate";
    }

    // 이미지 저장 경로
    private String getImageOutputDir() {
        // 로컬 환경
        return "/Users/<USER>/rd/labeling/crawling";
        // 배포 환경
//         return "/volume1/pj1/datalake/image";
    }

    /**
     * 다중 이미지 수집 처리 (비동기 방식 - 프로세스 실행 상태 확인 후 응답)
     */
    public List<ImageCollectionResultDto> collectImages(List<ImageCollectionReqDto> requests) {
        try {
            // Python 스크립트 실행을 위한 명령어 구성
            String pythonScriptPath = getPythonScriptPath();
            List<String> command = buildPythonCommand(pythonScriptPath, requests);

            // 실행할 명령어 로그 출력 (디버깅용)
            log.info("실행할 Python 명령어: {}", String.join(" ", command));
            log.info("Python 스크립트 경로: {}", pythonScriptPath);
            log.info("가상환경 경로: {}", getVirtualEnvPath());

            // 파일 존재 여부 확인
            if (!checkRequiredFiles()) {
                log.error("필수 파일들이 존재하지 않음");
                return requests.stream()
                        .map(req -> ImageCollectionResultDto.builder()
                                .source(req.getSource())
                                .keyword(req.getKeyword())
                                .status("실패")
                                .build())
                        .collect(Collectors.toList());
            }

            // Python 실행 환경 간단 검증
            if (!isPythonScriptExecutable(command)) {
                log.error("Python 실행 환경 문제 감지");
                return requests.stream()
                        .map(req -> ImageCollectionResultDto.builder()
                                .source(req.getSource())
                                .keyword(req.getKeyword())
                                .status("실패")
                                .build())
                        .collect(Collectors.toList());
            }

            // Python 스크립트를 비동기로 실행
            ProcessBuilder processBuilder = new ProcessBuilder(command);
            processBuilder.redirectErrorStream(true); // 에러 스트림을 표준 출력으로 리다이렉트
            Process process = processBuilder.start();

            log.info("Python 스크립트 실행 시작 - 요청 개수: {}", requests.size());

            // 프로세스가 실제로 시작되었는지 확인 (짧은 시간 대기)
            boolean processStartedSuccessfully = verifyProcessStartup(process);

            if (!processStartedSuccessfully) {
                log.error("Python 프로세스 시작 실패 또는 즉시 종료됨");
                // 프로세스가 아직 살아있다면 종료
                if (process.isAlive()) {
                    process.destroyForcibly();
                }
                return requests.stream()
                        .map(req -> ImageCollectionResultDto.builder()
                                .source(req.getSource())
                                .keyword(req.getKeyword())
                                .status("실패")
                                .build())
                        .collect(Collectors.toList());
            }

            // 백그라운드에서 프로세스 모니터링 (로그만)
            CompletableFuture.runAsync(() -> {
                try {
                    int exitCode = process.waitFor();
                    if (exitCode == 0) {
                        log.info("이미지 수집 프로세스 정상 완료");
                    } else {
                        log.error("Python 스크립트 실행 실패 - 종료 코드: {}", exitCode);
                    }
                } catch (InterruptedException e) {
                    log.error("프로세스 대기 중 인터럽트 발생", e);
                    Thread.currentThread().interrupt();
                }
            }, executorService);

            // 프로세스가 정상적으로 시작되었으므로 "수집 중" 상태로 응답 반환
            log.info("Python 프로세스 정상 시작 확인 완료");
            return requests.stream()
                    .map(req -> ImageCollectionResultDto.builder()
                            .source(req.getSource())
                            .keyword(req.getKeyword())
                            .status("수집 중")
                            .build())
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("이미지 수집 시작 중 오류 발생", e);
            return requests.stream()
                    .map(req -> ImageCollectionResultDto.builder()
                            .source(req.getSource())
                            .keyword(req.getKeyword())
                            .status("실패")
                            .build())
                    .collect(Collectors.toList());
        }
    }
    /**
     * Python 프로세스 시작 상태 검증
     * 프로세스가 실제로 시작되어 정상 실행 중인지 확인
     */
    private boolean verifyProcessStartup(Process process) {
        try {
            // 프로세스가 즉시 종료되지 않았는지 확인 (3초 대기)
            Thread.sleep(3000);

            if (!process.isAlive()) {
                log.error("Python 프로세스가 시작 후 즉시 종료됨");

                // 프로세스 종료 코드 확인
                try {
                    int exitCode = process.exitValue();
                    log.error("프로세스 종료 코드: {}", exitCode);
                } catch (IllegalThreadStateException e) {
                    log.error("프로세스 종료 코드를 가져올 수 없음");
                }

                // 프로세스 출력 및 에러 스트림 읽기
                captureProcessOutput(process);
                return false;
            }

            // 프로세스 출력을 확인하여 정상 시작 여부 판단
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                StringBuilder output = new StringBuilder();
                String line;

                // 사용 가능한 모든 출력 읽기
                while (reader.ready() && (line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                    log.info("Python 프로세스 출력: {}", line);

                    // 에러 메시지가 포함되어 있는지 확인
                    if (line.toLowerCase().contains("error") ||
                            line.toLowerCase().contains("exception") ||
                            line.toLowerCase().contains("failed") ||
                            line.toLowerCase().contains("traceback") ||
                            line.toLowerCase().contains("no such file")) {
                        log.error("Python 프로세스 시작 시 에러 감지: {}", line);
                        return false;
                    }
                }

                if (output.length() > 0) {
                    log.info("Python 프로세스 전체 출력:\n{}", output.toString());
                }
            }

            log.info("Python 프로세스 정상 시작 확인됨");
            return true;

        } catch (InterruptedException e) {
            log.error("프로세스 시작 검증 중 인터럽트 발생", e);
            Thread.currentThread().interrupt();
            return false;
        } catch (Exception e) {
            log.error("프로세스 시작 검증 중 오류 발생", e);
            return false;
        }
    }

    /**
     * 프로세스 출력 캡처 (디버깅용)
     */
    private void captureProcessOutput(Process process) {
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
            String line;
            log.error("=== 프로세스 출력 캡처 시작 ===");
            while ((line = reader.readLine()) != null) {
                log.error("프로세스 출력: {}", line);
            }
            log.error("=== 프로세스 출력 캡처 종료 ===");
        } catch (Exception e) {
            log.error("프로세스 출력 캡처 중 오류", e);
        }
    }

    /**
     * 필수 파일들의 존재 여부 확인
     */
    private boolean checkRequiredFiles() {
        String pythonScriptPath = getPythonScriptPath();
        String virtualEnvPath = getVirtualEnvPath();

        // Python 스크립트 파일 존재 확인
        File pythonScript = new File(pythonScriptPath);
        if (!pythonScript.exists()) {
            log.error("Python 스크립트 파일이 존재하지 않음: {}", pythonScriptPath);
            return false;
        }

        // 가상환경 활성화 스크립트 존재 확인
        File virtualEnvScript = new File(virtualEnvPath);
        if (!virtualEnvScript.exists()) {
            log.error("가상환경 활성화 스크립트가 존재하지 않음: {}", virtualEnvPath);
            return false;
        }

        log.info("필수 파일들 존재 확인 완료");
        return true;
    }

    /**
     * Python 스크립트 실행 상태 확인 (간단한 검증)
     */
    private boolean isPythonScriptExecutable(List<String> command) {
        try {
            ProcessBuilder testBuilder = new ProcessBuilder(command.get(0), "-c", "echo 'test'");
            Process testProcess = testBuilder.start();
            int exitCode = testProcess.waitFor();
            return exitCode == 0;
        } catch (Exception e) {
            log.warn("Python 실행 환경 확인 실패: {}", e.getMessage());
            return false;
        }
    }






    /**
     * 실시간 데이터 수집 현황 조회 (10초 간격 폴링용) - 사용자별 필터링
     * 실제 DB 데이터를 조회하여 실시간 진행률 제공
     */
    public Map<String, Object> getCrawlingStatus() {
        log.info("실시간 수집 현황 조회 시작");

        try {
            // 현재 로그인한 사용자 이메일 가져오기
            String currentUserEmail = SecurityUtil.getCurrentUserEmail();

            List<TargetData> targetDataList;
            if (currentUserEmail != null) {
                // 로그인한 사용자의 데이터만 조회
                targetDataList = targetDataRepository.findAllOrderByStartDateDescAndCreatedBy(currentUserEmail);
                log.info("사용자별 데이터 조회 - 사용자: {}, 데이터 개수: {}", currentUserEmail, targetDataList.size());
            } else {
                // 로그인하지 않은 경우 모든 데이터 조회 (기존 동작 유지)
                targetDataList = targetDataRepository.findAllOrderByStartDateDesc();
                log.info("전체 데이터 조회 - 데이터 개수: {}", targetDataList.size());
            }

            List<Map<String, Object>> dataList = new ArrayList<>();

            for (TargetData targetData : targetDataList) {
                // 해당 job_id로 실제 수집된 이미지 개수 조회 (사용자별 필터링)
                Long actualCollectedCount;
                if (currentUserEmail != null) {
                    actualCollectedCount = imageMetadataRepository.countByJobIdAndCreatedBy(targetData.getJobId(), currentUserEmail);
                } else {
                    actualCollectedCount = imageMetadataRepository.countByJobId(targetData.getJobId());
                }

                Map<String, Object> data = new HashMap<>();
                data.put("job_id", targetData.getJobId());
                data.put("keyword", targetData.getKeyword());
                data.put("source", targetData.getSource());
                data.put("start_date", targetData.getStartDate());
                data.put("end_date", targetData.getEndDate());
                data.put("target_count", targetData.getTargetCount());
                data.put("collected_count", actualCollectedCount); // 실제 수집된 개수
                data.put("processing_count", targetData.getProcessingCount());
                data.put("status", targetData.getStats());
                data.put("file_path", targetData.getFilePath());

                // 진행률 계산
                double progress = targetData.getTargetCount() > 0 ?
                        (double) actualCollectedCount / targetData.getTargetCount() * 100 : 0;
                data.put("progress", Math.round(progress * 100.0) / 100.0); // 소수점 2자리

                dataList.add(data);
            }

            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("message", "실시간 수집 현황 조회 성공");
            response.put("data", dataList);
            response.put("timestamp", System.currentTimeMillis()); // 조회 시점

            log.info("실시간 수집 현황 조회 완료 - 총 작업 수: {}", dataList.size());

            return response;

        } catch (Exception e) {
            log.error("실시간 수집 현황 조회 중 오류 발생", e);

            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", 500);
            errorResponse.put("message", "실시간 수집 현황 조회 실패: " + e.getMessage());
            errorResponse.put("data", new ArrayList<>());
            errorResponse.put("timestamp", System.currentTimeMillis());

            return errorResponse;
        }
    }


    // 데이터 수집 카운트 현황 조회 (하드코딩)
    public Map<String, Object> getCrawlingCount() {
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "데이터 수집현황 조회 성공");

        List<Map<String, Object>> dataList = new ArrayList<>();

        // 첫 번째 데이터
        Map<String, Object> data1 = new HashMap<>();
        data1.put("source", "youtube");
        data1.put("keyword", "아이유");
        data1.put("target_count", 60);
        data1.put("collected_count", 29);
        dataList.add(data1);

        // 두 번째 데이터
        Map<String, Object> data2 = new HashMap<>();
        data2.put("source", "google");
        data2.put("keyword", "남해");
        data2.put("target_count", 20);
        data2.put("collected_count", 7);
        dataList.add(data2);

        // 세 번째 데이터
        Map<String, Object> data3 = new HashMap<>();
        data3.put("source", "pixabay");
        data3.put("keyword", "바다");
        data3.put("target_count", 20);
        data3.put("collected_count", 5);
        dataList.add(data3);

        // 네 번째 데이터
        Map<String, Object> data4 = new HashMap<>();
        data4.put("source", "unsplash");
        data4.put("keyword", "남해");
        data4.put("target_count", 20);
        data4.put("collected_count", 3);
        dataList.add(data4);

        response.put("data", dataList);


        return response;
    }



    /**
     * 프론트에서 선택한 job_id들의 실시간 데이터 수집 현황 조회 (10초 간격 폴링용)
     * 사용자가 프론트에서 선택한 작업들의 job_id를 파라미터로 받아서 처리
     * @param jobIdsParam 조회할 작업 ID들 (쉼표로 구분된 문자열, 예: "job1,job2,job3")
     * @return 선택된 작업들의 실시간 수집 현황
     */
    public Map<String, Object> getCrawlingCountByJobIds(String jobIdsParam) {
        log.info("프론트에서 선택한 job_id들의 실시간 수집현황 조회 시작 - jobIds: {}", jobIdsParam);

        try {
            // 현재 로그인한 사용자 이메일 가져오기
            String currentUserEmail = SecurityUtil.getCurrentUserEmail();

            // 파라미터 검증
            if (jobIdsParam == null || jobIdsParam.trim().isEmpty()) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("code", 400);
                errorResponse.put("message", "job_id 파라미터가 필요합니다. (예: jobId1,jobId2,jobId3)");
                errorResponse.put("data", new ArrayList<>());
                errorResponse.put("timestamp", System.currentTimeMillis());
                return errorResponse;
            }

            // 쉼표로 구분된 job_id들을 List로 변환
            List<String> jobIds = Arrays.stream(jobIdsParam.split(","))
                    .map(String::trim)
                    .filter(id -> !id.isEmpty())
                    .collect(Collectors.toList());

            if (jobIds.isEmpty()) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("code", 400);
                errorResponse.put("message", "유효한 job_id가 없습니다.");
                errorResponse.put("data", new ArrayList<>());
                errorResponse.put("timestamp", System.currentTimeMillis());
                return errorResponse;
            }

            // 여러 job_id들로 target_data 조회 (사용자별 필터링)
            List<TargetData> targetDataList;
            if (currentUserEmail != null) {
                targetDataList = targetDataRepository.findByJobIdInAndCreatedBy(jobIds, currentUserEmail);
                log.info("사용자별 job_id 데이터 조회 - 사용자: {}, 조회된 데이터 개수: {}", currentUserEmail, targetDataList.size());
            } else {
                targetDataList = targetDataRepository.findByJobIdIn(jobIds);
                log.info("전체 job_id 데이터 조회 - 조회된 데이터 개수: {}", targetDataList.size());
            }

            if (targetDataList.isEmpty()) {
                Map<String, Object> notFoundResponse = new HashMap<>();
                notFoundResponse.put("code", 404);
                notFoundResponse.put("message", "해당 job_id들의 데이터를 찾을 수 없습니다: " + String.join(", ", jobIds));
                notFoundResponse.put("data", new ArrayList<>());
                notFoundResponse.put("timestamp", System.currentTimeMillis());
                notFoundResponse.put("requested_job_ids", jobIds);
                return notFoundResponse;
            }

            List<Map<String, Object>> resultList = new ArrayList<>();

            for (TargetData targetData : targetDataList) {
                // 해당 job_id로 실제 수집된 이미지 개수 조회 (사용자별 필터링)
                Long actualCollectedCount;
                if (currentUserEmail != null) {
                    actualCollectedCount = imageMetadataRepository.countByJobIdAndCreatedBy(targetData.getJobId(), currentUserEmail);
                } else {
                    actualCollectedCount = imageMetadataRepository.countByJobId(targetData.getJobId());
                }

                Map<String, Object> data = new HashMap<>();
                data.put("job_id", targetData.getJobId());
                data.put("source", targetData.getSource());
                data.put("keyword", targetData.getKeyword());
                data.put("target_count", targetData.getTargetCount());
                data.put("collected_count", actualCollectedCount); // 실제 수집된 개수
                data.put("processing_count", targetData.getProcessingCount());
                data.put("status", targetData.getStats());
                data.put("start_date", targetData.getStartDate());
                data.put("end_date", targetData.getEndDate());
                data.put("file_path", targetData.getFilePath());

                // 진행률 계산
                double progress = targetData.getTargetCount() > 0 ?
                        (double) actualCollectedCount / targetData.getTargetCount() * 100 : 0;
                data.put("progress", Math.round(progress * 100.0) / 100.0); // 소수점 2자리

                resultList.add(data);
            }

            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("message", "선택한 job_id들의 실시간 수집현황 조회 성공");
            response.put("data", resultList);
            response.put("timestamp", System.currentTimeMillis()); // 조회 시점
            response.put("requested_job_ids", jobIds); // 요청된 job_id 목록
            response.put("found_job_ids", resultList.stream()
                    .map(item -> (String) item.get("job_id"))
                    .distinct()
                    .collect(Collectors.toList())); // 실제 조회된 job_id 목록

            log.info("프론트에서 선택한 job_id들의 실시간 수집현황 조회 완료 - 요청 job_id 개수: {}, 총 항목 수: {}",
                    jobIds.size(), resultList.size());

            return response;

        } catch (Exception e) {
            log.error("프론트에서 선택한 job_id들의 실시간 수집현황 조회 중 오류 발생", e);

            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", 500);
            errorResponse.put("message", "선택한 job_id들의 실시간 수집현황 조회 실패: " + e.getMessage());
            errorResponse.put("data", new ArrayList<>());
            errorResponse.put("timestamp", System.currentTimeMillis());

            return errorResponse;
        }
    }

    /**
     * 특정 job_id에 대한 실시간 데이터 수집 현황 조회 (10초 간격 폴링용)
     * @param jobId 조회할 작업 ID
     * @return 수집 현황 데이터
     */
    public Map<String, Object> getCrawlingCount(String jobId) {
        log.info("특정 job_id 실시간 수집현황 조회 시작 - jobId: {}", jobId);

        try {
            // target_data 테이블에서 특정 job_id의 데이터만 조회
            List<TargetData> targetDataList = targetDataRepository.findByJobId(jobId);

            if (targetDataList.isEmpty()) {
                Map<String, Object> notFoundResponse = new HashMap<>();
                notFoundResponse.put("code", 404);
                notFoundResponse.put("message", "해당 job_id의 데이터를 찾을 수 없습니다: " + jobId);
                notFoundResponse.put("data", new ArrayList<>());
                notFoundResponse.put("timestamp", System.currentTimeMillis());
                return notFoundResponse;
            }

            List<Map<String, Object>> resultList = new ArrayList<>();

            for (TargetData targetData : targetDataList) {
                // 해당 job_id로 실제 수집된 이미지 개수 조회
                Long actualCollectedCount = imageMetadataRepository.countByJobId(targetData.getJobId());

                Map<String, Object> data = new HashMap<>();
                data.put("job_id", targetData.getJobId());
                data.put("source", targetData.getSource());
                data.put("keyword", targetData.getKeyword());
                data.put("target_count", targetData.getTargetCount());
                data.put("collected_count", actualCollectedCount); // 실제 수집된 개수
                data.put("processing_count", targetData.getProcessingCount());
                data.put("status", targetData.getStats());
                data.put("start_date", targetData.getStartDate());
                data.put("end_date", targetData.getEndDate());
                data.put("file_path", targetData.getFilePath());

                // 진행률 계산
                double progress = targetData.getTargetCount() > 0 ?
                        (double) actualCollectedCount / targetData.getTargetCount() * 100 : 0;
                data.put("progress", Math.round(progress * 100.0) / 100.0); // 소수점 2자리

                // 상태별 추가 정보
                if ("완료".equals(targetData.getStats())) {
                    data.put("completion_time", targetData.getEndDate());
                } else if ("수집 중".equals(targetData.getStats())) {
                    data.put("estimated_remaining", estimateRemainingTime(targetData, actualCollectedCount));
                }

                resultList.add(data);
            }

            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("message", "특정 job_id 실시간 수집현황 조회 성공");
            response.put("data", resultList);
            response.put("timestamp", System.currentTimeMillis()); // 조회 시점
            response.put("job_id", jobId);

            log.info("특정 job_id 실시간 수집현황 조회 완료 - jobId: {}, 총 항목 수: {}", jobId, resultList.size());

            return response;

        } catch (Exception e) {
            log.error("특정 job_id 실시간 수집현황 조회 중 오류 발생", e);

            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", 500);
            errorResponse.put("message", "특정 job_id 실시간 수집현황 조회 실패: " + e.getMessage());
            errorResponse.put("data", new ArrayList<>());
            errorResponse.put("timestamp", System.currentTimeMillis());
            errorResponse.put("job_id", jobId);

            return errorResponse;
        }
    }

    /**
     * 남은 시간 추정 (간단한 계산)
     * @param targetData 타겟 데이터
     * @param currentCount 현재 수집된 개수
     * @return 추정 남은 시간 (분)
     */
    private String estimateRemainingTime(TargetData targetData, Long currentCount) {
        try {
            if (currentCount == 0 || targetData.getStartDate() == null) {
                return "계산 불가";
            }

            long elapsedMinutes = (System.currentTimeMillis() - targetData.getStartDate().getTime()) / (1000 * 60);
            if (elapsedMinutes == 0) {
                return "계산 중";
            }

            double rate = (double) currentCount / elapsedMinutes; // 분당 수집률
            long remaining = targetData.getTargetCount() - currentCount;

            if (rate > 0) {
                long estimatedMinutes = (long) (remaining / rate);
                return estimatedMinutes + "분 예상";
            }

            return "계산 불가";
        } catch (Exception e) {
            return "계산 오류";
        }
    }

    // 키워드별 데이터 보유 현황 조회 (실제 DB 조회) - 사용자별 필터링
    public Map<String, Object> getKeywordCount() {
        log.info("키워드별 데이터 보유 현황 조회 시작");

        try {
            // 현재 로그인한 사용자 이메일 가져오기
            String currentUserEmail = SecurityUtil.getCurrentUserEmail();

            List<KeywordCountDto> keywordCounts;
            if (currentUserEmail != null) {
                // 로그인한 사용자의 데이터만 조회
                keywordCounts = imageMetadataRepository.findKeywordCountsByCreatedBy(currentUserEmail);
                log.info("사용자별 키워드 카운트 조회 - 사용자: {}, 키워드 개수: {}", currentUserEmail, keywordCounts.size());
            } else {
                // 로그인하지 않은 경우 모든 데이터 조회 (기존 동작 유지)
                keywordCounts = imageMetadataRepository.findKeywordCounts();
                log.info("전체 키워드 카운트 조회 - 키워드 개수: {}", keywordCounts.size());
            }

            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("message", "전체 키워드 보유 현황 조회 성공");
            response.put("data", keywordCounts);

            log.info("키워드별 데이터 보유 현황 조회 완료 - 총 키워드 수: {}", keywordCounts.size());

            return response;

        } catch (Exception e) {
            log.error("키워드별 데이터 보유 현황 조회 중 오류 발생", e);

            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", 500);
            errorResponse.put("message", "전체 키워드 보유 현황 조회 실패: " + e.getMessage());
            errorResponse.put("data", new ArrayList<>());

            return errorResponse;
        }
    }

    /**
     * 특정 jobId에 해당하는 데이터 삭제
     * @param jobId 삭제할 작업 ID
     * @return 삭제 결과
     */
    @Transactional
    public Map<String, Object> deleteCrawlingData(String jobId) {
        log.info("데이터 삭제 시작 - jobId: {}", jobId);

        Map<String, Object> response = new HashMap<>();

        try {
            // 1. 해당 jobId가 존재하는지 확인
            List<TargetData> targetDataList = targetDataRepository.findByJobId(jobId);
            if (targetDataList.isEmpty()) {
                log.warn("삭제할 데이터가 존재하지 않음 - jobId: {}", jobId);
                response.put("code", 404);
                response.put("message", "해당 jobId의 데이터가 존재하지 않습니다.");
                return response;
            }

            // 2. 삭제 전 데이터 개수 확인 (로그용)
            Long imageCount = imageMetadataRepository.countByJobId(jobId);
            log.info("삭제 예정 데이터 - jobId: {}, 타겟 데이터: {}개, 이미지 메타데이터: {}개",
                    jobId, targetDataList.size(), imageCount);

            // 3. 이미지 메타데이터 삭제
            imageMetadataRepository.deleteByJobId(jobId);
            log.info("이미지 메타데이터 삭제 완료 - jobId: {}", jobId);

            // 4. 타겟 데이터 삭제
            targetDataRepository.deleteByJobId(jobId);
            log.info("타겟 데이터 삭제 완료 - jobId: {}", jobId);

            response.put("code", 200);
            response.put("message", "데이터 삭제 성공");
            response.put("deletedJobId", jobId);
            response.put("deletedImageCount", imageCount);
            response.put("deletedTargetDataCount", targetDataList.size());

            log.info("데이터 삭제 완료 - jobId: {}", jobId);

        } catch (Exception e) {
            log.error("데이터 삭제 중 오류 발생 - jobId: {}", jobId, e);

            response.put("code", 500);
            response.put("message", "데이터 삭제 실패: " + e.getMessage());
            response.put("jobId", jobId);
        }

        return response;
    }

    /**
     * PythonRunComponent를 사용한 이미지 크롤링 실행 (비동기 처리)
     * @param requests 크롤링 요청 리스트
     * @return 크롤링 응답 리스트 (즉시 반환, job_id는 임시 생성)
     */
    public List<ImageCrawlingResponseDto> executeImageCrawling(List<ImageCrawlingRequestDto> requests) {
        log.info("이미지 크롤링 실행 시작 - 요청 개수: {}", requests.size());

        List<ImageCrawlingResponseDto> responses = new ArrayList<>();

        try {
            // 현재 로그인한 사용자 이메일 가져오기
            String currentUserEmail = SecurityUtil.getCurrentUserEmailOrDefault("<EMAIL>");
            log.info("크롤링 실행 사용자: {}", currentUserEmail);

            // 요청 데이터를 Map 형태로 변환 (PythonRunComponent에서 요구하는 형식)
            List<Map<String, Object>> jsonData = requests.stream()
                    .map(req -> {
                        Map<String, Object> map = new HashMap<>();
                        map.put("source", req.getSource());
                        map.put("keyword", req.getKeyword());
                        map.put("target_count", req.getTarget_count());
                        map.put("created_by", currentUserEmail); // 사용자 정보 추가
                        return map;
                    })
                    .collect(Collectors.toList());

            log.info("Python 스크립트로 전달할 데이터: {}", jsonData);

            // PythonRunComponent를 사용하여 비동기로 크롤링 실행
            String executionResult = pythonRunComponent.runCrawlingPythonAsync(jsonData);

            log.info("Python 크롤링 실행 결과: {}", executionResult);

            // 실행 결과에 따라 응답 생성
            if ("started".equals(executionResult)) {
                // 성공적으로 시작된 경우, 각 요청에 대해 임시 job_id를 생성하여 즉시 응답
                responses = requests.stream()
                        .map(req -> {
                            String jobId = generateJobId(); // 임시 job_id 생성
                            return ImageCrawlingResponseDto.builder()
                                    .source(req.getSource())
                                    .keyword(req.getKeyword())
                                    .target_count(req.getTarget_count())
                                    .job_id(jobId)
                                    .status("수집 중")
                                    .message("크롤링이 성공적으로 시작되었습니다.")
                                    .build();
                        })
                        .collect(Collectors.toList());

                // 백그라운드에서 실제 job_id 업데이트 작업 수행
                CompletableFuture.runAsync(() -> {
                    try {
                        log.info("백그라운드에서 실제 job_id 추출 작업 시작");
                        // 잠시 대기 후 Python 스크립트 출력에서 실제 job_id 추출 시도
                        Thread.sleep(5000); // 5초 대기
                        // 실제 구현에서는 데이터베이스에서 최신 job_id를 조회하거나
                        // Python 스크립트의 로그 파일을 모니터링할 수 있습니다.
                        log.info("실제 job_id는 데이터베이스 조회를 통해 확인 가능합니다.");
                    } catch (Exception e) {
                        log.error("백그라운드 job_id 업데이트 중 오류", e);
                    }
                }, executorService);

            } else {
                // 실행 실패한 경우
                responses = requests.stream()
                        .map(req -> ImageCrawlingResponseDto.builder()
                                .source(req.getSource())
                                .keyword(req.getKeyword())
                                .target_count(req.getTarget_count())
                                .job_id(null)
                                .status("실패")
                                .message("크롤링 실행에 실패했습니다: " + executionResult)
                                .build())
                        .collect(Collectors.toList());
            }

        } catch (Exception e) {
            log.error("이미지 크롤링 실행 중 오류 발생", e);

            // 예외 발생 시 모든 요청에 대해 실패 응답 생성
            responses = requests.stream()
                    .map(req -> ImageCrawlingResponseDto.builder()
                            .source(req.getSource())
                            .keyword(req.getKeyword())
                            .target_count(req.getTarget_count())
                            .job_id(null)
                            .status("오류")
                            .message("크롤링 실행 중 오류가 발생했습니다: " + e.getMessage())
                            .build())
                    .collect(Collectors.toList());
        }

        log.info("이미지 크롤링 실행 완료 - 응답 개수: {}", responses.size());
        return responses;
    }

    /**
     * Python 출력에서 job_id들을 추출하는 메서드
     * @param output Python 스크립트의 출력
     * @return source:keyword를 키로 하고 job_id를 값으로 하는 맵
     */
    private Map<String, String> extractJobIdsFromOutput(String output) {
        Map<String, String> jobIdMap = new HashMap<>();

        try {
            String[] lines = output.split("\n");
            boolean inJobIdSection = false;

            for (String line : lines) {
                line = line.trim();

                if ("JOB_IDS_START".equals(line)) {
                    inJobIdSection = true;
                    continue;
                }

                if ("JOB_IDS_END".equals(line)) {
                    inJobIdSection = false;
                    break;
                }

                if (inJobIdSection && line.startsWith("JOB_ID:")) {
                    // 형식: JOB_ID:source:keyword:job_id
                    String[] parts = line.split(":", 4);
                    if (parts.length == 4) {
                        String source = parts[1];
                        String keyword = parts[2];
                        String jobId = parts[3];
                        String key = source + ":" + keyword;
                        jobIdMap.put(key, jobId);
                        log.info("추출된 job_id - {}:{} -> {}", source, keyword, jobId);
                    }
                }
            }

        } catch (Exception e) {
            log.error("job_id 추출 중 오류 발생", e);
        }

        return jobIdMap;
    }

    /**
     * 고유한 job_id 생성 (백업용)
     * @return UUID 기반 job_id
     */
    private String generateJobId() {
        return UUID.randomUUID().toString();
    }

}



