package kr.co.digitalzone.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ImageCrawlingRequestDto {

    @NotBlank(message = "소스는 필수입니다")
    private String source;

    @NotBlank(message = "키워드는 필수입니다")
    private String keyword;

    @NotNull(message = "목표 수집 개수는 필수입니다")
    @Min(value = 1, message = "목표 수집 개수는 1 이상이어야 합니다")
    private Integer target_count;

    // JWT 토큰에서 추출한 사용자 이메일 (서버에서 자동 설정)
    private String created_by;
}
