package kr.co.digitalzone.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ImageCrawlingResponseDto {

    private String source;
    private String keyword;
    private Integer target_count;
    private String job_id;
    private String status;
    private String message;
    private String created_by; // 사용자 정보
}
