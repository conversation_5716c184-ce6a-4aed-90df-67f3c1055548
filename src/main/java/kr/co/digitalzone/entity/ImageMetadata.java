package kr.co.digitalzone.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "image_metadata")
@DynamicUpdate
@DynamicInsert
public class ImageMetadata {
    
    @Id
    @Column(name = "id")
    private String id;
    
    @Column(name = "keyword")
    private String keyword;
    
    @Column(name = "registered_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date registeredDate;
    
    @Column(name = "file_path")
    private String filePath;
    
    @Column(name = "source")
    private String source;

    @Column(name = "job_id") // 추가: 어떤 수집 작업에 의해 생성되었는지
    private String jobId;

    @Column(name = "file_name")
    private String fileName;

    @Column(name = "video_url")
    private String videoUrl;

    @Column(name = "created_by") // 추가: 크롤링을 실행한 사용자 이메일
    private String createdBy;
}
