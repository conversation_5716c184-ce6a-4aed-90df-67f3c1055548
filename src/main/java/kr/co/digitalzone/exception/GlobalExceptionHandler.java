package kr.co.digitalzone.exception;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;

import java.io.IOException;
import java.net.SocketException;

@ControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    /**
     * Broken pipe 및 연결 관련 오류 처리
     */
    @ExceptionHandler({IOException.class, SocketException.class})
    public ResponseEntity<Object> handleConnectionException(Exception ex, WebRequest request) {
        String errorMessage = ex.getMessage();
        
        // Broken pipe 오류인지 확인
        if (errorMessage != null && (
                errorMessage.contains("Broken pipe") ||
                errorMessage.contains("Connection reset") ||
                errorMessage.contains("Connection aborted") ||
                errorMessage.contains("CLOSE_CONNECTION_NOW"))) {
            
            log.warn("클라이언트 연결 끊어짐 감지: {} - URI: {}", 
                    errorMessage, request.getDescription(false));
            
            // 클라이언트가 연결을 끊었으므로 응답하지 않음
            return null;
        }
        
        // 다른 I/O 오류는 일반적인 서버 오류로 처리
        log.error("I/O 오류 발생: {}", errorMessage, ex);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("서버 내부 오류가 발생했습니다.");
    }

    /**
     * 일반적인 예외 처리
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<Object> handleGenericException(Exception ex, WebRequest request) {
        log.error("예상치 못한 오류 발생: {} - URI: {}", 
                ex.getMessage(), request.getDescription(false), ex);
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("서버 내부 오류가 발생했습니다: " + ex.getMessage());
    }
}
