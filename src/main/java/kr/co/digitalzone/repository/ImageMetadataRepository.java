package kr.co.digitalzone.repository;

import kr.co.digitalzone.dto.KeywordCountDto;
import kr.co.digitalzone.entity.ImageMetadata;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ImageMetadataRepository extends JpaRepository<ImageMetadata, String> {
    
    /**
     * 모든 키워드별 이미지 개수 조회
     * @return 키워드별 이미지 개수 리스트 (개수 내림차순 정렬)
     */
    @Query("SELECT new kr.co.digitalzone.dto.KeywordCountDto(im.keyword, COUNT(im.id)) " +
           "FROM ImageMetadata im " +
           "GROUP BY im.keyword ")
    List<KeywordCountDto> findKeywordCounts();

    /**
     * 특정 사용자의 키워드별 이미지 개수 조회
     * @param createdBy 사용자 이메일
     * @return 해당 사용자의 키워드별 이미지 개수 리스트
     */
    @Query("SELECT new kr.co.digitalzone.dto.KeywordCountDto(im.keyword, COUNT(im.id)) " +
           "FROM ImageMetadata im " +
           "WHERE im.createdBy = :createdBy " +
           "GROUP BY im.keyword ")
    List<KeywordCountDto> findKeywordCountsByCreatedBy(@Param("createdBy") String createdBy);

//    /**
//     * 특정 키워드와 소스의 이미지 개수 조회
//     */
//    @Query("SELECT COUNT(im.id) FROM ImageMetadata im WHERE im.keyword = :keyword AND im.source = :source")
//    Long countByKeywordAndSource(@Param("keyword") String keyword, @Param("source") String source);

    @Query("SELECT COUNT(im.id) FROM ImageMetadata im WHERE im.jobId = :jobId")
    Long countByJobId(@Param("jobId") String jobId);

    /**
     * 특정 사용자의 job_id별 이미지 개수 조회
     * @param jobId job ID
     * @param createdBy 사용자 이메일
     * @return 해당 사용자의 job_id별 이미지 개수
     */
    @Query("SELECT COUNT(im.id) FROM ImageMetadata im WHERE im.jobId = :jobId AND im.createdBy = :createdBy")
    Long countByJobIdAndCreatedBy(@Param("jobId") String jobId, @Param("createdBy") String createdBy);

    /**
     * 특정 job_id의 이미지 메타데이터 삭제
     */
    @Modifying
    @Query("DELETE FROM ImageMetadata im WHERE im.jobId = :jobId")
    void deleteByJobId(@Param("jobId") String jobId);

    /**
     * 특정 사용자의 job_id 이미지 메타데이터 삭제
     */
    @Modifying
    @Query("DELETE FROM ImageMetadata im WHERE im.jobId = :jobId AND im.createdBy = :createdBy")
    void deleteByJobIdAndCreatedBy(@Param("jobId") String jobId, @Param("createdBy") String createdBy);

}


