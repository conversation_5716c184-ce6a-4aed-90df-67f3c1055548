package kr.co.digitalzone.user.common.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.web.PageableHandlerMethodArgumentResolver;
import org.springframework.data.web.SortHandlerMethodArgumentResolver;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.AsyncSupportConfigurer;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

@Configuration
@Slf4j
public class WebConfig implements WebMvcConfigurer {

    @Value("${spring.file.upload.path}")
    String filePath;

    @Value("${spring.file.thumbnail.path}")
    String thumbnailPath;

    @Value("${spring.file.download.path}")
    String downloadPath;

    @Value("${spring.file.worked.path}")
    String workedPath;




    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> argumentResolvers) {
        SortHandlerMethodArgumentResolver sortArgumentResolver = new SortHandlerMethodArgumentResolver();
        sortArgumentResolver.setSortParameter("sort");
        sortArgumentResolver.setPropertyDelimiter("-");

        PageableHandlerMethodArgumentResolver pageableArgumentResolver = new PageableHandlerMethodArgumentResolver(sortArgumentResolver);
        pageableArgumentResolver.setOneIndexedParameters(true);
        pageableArgumentResolver.setMaxPageSize(50);
        pageableArgumentResolver.setFallbackPageable(PageRequest.of(0, 10));
        argumentResolvers.add(pageableArgumentResolver);

    }

    @Override
    public void configureAsyncSupport(AsyncSupportConfigurer configurer) {
        // 비동기 요청 타임아웃 설정 (30초)
        configurer.setDefaultTimeout(30000);

        // 비동기 처리용 스레드 풀 설정
        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        taskExecutor.setCorePoolSize(5);
        taskExecutor.setMaxPoolSize(10);
        taskExecutor.setQueueCapacity(25);
        taskExecutor.setThreadNamePrefix("async-");
        taskExecutor.initialize();

        configurer.setTaskExecutor(taskExecutor);

        log.info("비동기 지원 설정 완료 - 타임아웃: 30초, 코어 스레드: 5개, 최대 스레드: 10개");
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // file:///C:/Users/<USER>/labeling/
        String tktktk = "file:"+filePath;
//        log.info("file:"+filePath+"/");
        registry.addResourceHandler("/upload/**")
//                .addResourceLocations("file:/Users/<USER>/rd/labeling/upload/");
                .addResourceLocations("file:"+filePath+"/");
//                .addResourceLocations("file:/home/<USER>/datalake/upload/");
        registry.addResourceHandler("/thumbnail/**")
//                .addResourceLocations("file:/home/<USER>/datalake/thumbnail/");
                .addResourceLocations("file:"+thumbnailPath+"/");
        registry.addResourceHandler("/download/**")
                .addResourceLocations("file:"+downloadPath+"/");
//                .addResourceLocations("file:/home/<USER>/datalake/download/");
        registry.addResourceHandler("/worked/**")
                .addResourceLocations("file:"+workedPath+"/");
//                .addResourceLocations("file:/home/<USER>/datalake/worked/");
    }
}
