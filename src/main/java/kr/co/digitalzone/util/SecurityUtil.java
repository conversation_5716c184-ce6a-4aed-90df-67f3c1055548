package kr.co.digitalzone.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class SecurityUtil {

    /**
     * 현재 로그인한 사용자의 이메일을 가져옵니다.
     * @return 사용자 이메일 (로그인하지 않은 경우 null)
     */
    public static String getCurrentUserEmail() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            
            if (authentication != null && authentication.isAuthenticated() 
                && !"anonymousUser".equals(authentication.getPrincipal())) {
                
                String email = authentication.getName();
                log.debug("현재 로그인한 사용자 이메일: {}", email);
                return email;
            }
            
            log.debug("인증되지 않은 사용자 또는 익명 사용자");
            return null;
            
        } catch (Exception e) {
            log.error("현재 사용자 정보 조회 중 오류 발생", e);
            return null;
        }
    }

    /**
     * 현재 로그인한 사용자의 이메일을 가져옵니다. (기본값 포함)
     * @param defaultEmail 로그인하지 않은 경우 사용할 기본 이메일
     * @return 사용자 이메일 또는 기본 이메일
     */
    public static String getCurrentUserEmailOrDefault(String defaultEmail) {
        String email = getCurrentUserEmail();
        return email != null ? email : defaultEmail;
    }

    /**
     * 사용자가 로그인했는지 확인합니다.
     * @return 로그인 여부
     */
    public static boolean isAuthenticated() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            return authentication != null && authentication.isAuthenticated() 
                && !"anonymousUser".equals(authentication.getPrincipal());
        } catch (Exception e) {
            log.error("인증 상태 확인 중 오류 발생", e);
            return false;
        }
    }
}
