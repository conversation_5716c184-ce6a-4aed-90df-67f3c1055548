server:
  port: 8080
  # 연결 타임아웃 및 Broken pipe 방지 설정
  tomcat:
    connection-timeout: 60000  # 60초
    keep-alive-timeout: 30000  # 30초
    max-keep-alive-requests: 100
    threads:
      max: 200
      min-spare: 10
  # HTTP 연결 설정
  http2:
    enabled: true
  compression:
    enabled: true

# 운영시 삭제
decorator:
  datasource:
    p6spy:
      enable-logging: true



spring:
  mail:
    host: smtp.gmail.com
    port: 587
    username: dataskpark
    password: skom ftee varq lskq
    properties:
      mail:
        smtp:
          auth: true
          timeout: 5000
          starttls:
            enable: true
  mvc:
    throw-exception-if-no-handler-found: true
  web:
    resources:
      static-locations: META-INF:/resources, classpath:/resources, classpath:/static, classpath:/static/dist
  # Database
  datasource:
    driver-class-name: org.postgresql.Driver
    # url: ************************************************
    url: ************************************************
    #    url: ************************************
    username: admin
    password: signgate1!
  # File
  servlet:
    multipart:
      enabled: true #업로드 지원여부
      file-size-threshold: 0B #메모리에 기록되는 값
      max-file-size: 10MB #파일 최대 사이즈
      max-request-size: 100MB #요청 최대  :
      resolve-lazily: true
  file:
    upload:
#      path: /Users/<USER>/rd/labeling/upload
#          path : /home/<USER>/datalake/upload
      path: /volume1/pj1/datalake/labeling/upload
    download:
#      path: /Users/<USER>/rd/labeling/download
#          path: /home/<USER>/datalake/download
      path: /volume1/pj1/datalake/labeling/download
    thumbnail:
#      path: /Users/<USER>/rd/labeling/thumbnail
#          path: /home/<USER>/datalake/thumbnail
      path: /volume1/pj1/datalake/labeling/thumbnail
    worked:
#      path: /Users/<USER>/rd/labeling/worked
#      path: /home/<USER>/datalake/worked
      path: /volume1/pj1/datalake/labeling/worked

  jpa:
    hibernate:
      ddl-auto: none
    database: postgresql
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    open-in-view: false
    properties:
      hibernate:
        default_batch_fetch_size: 100
        format_sql: true
        show_sql: false
        use_sql_comments: true
  http:
    multipart:
      file-encoding: UTF-8
springdoc:
  packages-to-scan:
    - kr.co.digitalzone.member.controller
    - kr.co.digitalzone.user.jobItem.controller
    - kr.co.digitalzone.user.project.controller
    - kr.co.digitalzone.user.classItem.controller
    - kr.co.digitalzone.crawling.images.controller

  swagger-ui:
    path: /admin/swagger-ui/index.html




logging.level:
  org:
    hibernate:
      SQL: debug #콘솔에 남기는게 아니라 로그로 남음.
    apache:
      coyote:
        http11: debug
      catalina:
        connector: warn  # Broken pipe 오류 로그 레벨 조정
    springframework:
      web:
        servlet: warn  # 서블릿 관련 오류 로그 레벨 조정

      type:
        descriptor.sql:
          BasicBinder: trace
    springboot: debug

  kr:
    co:
      digitalzone: debug

  # 연결 관련 오류 로그 레벨 조정
  root: info




keys:
  password:
    secret: MyqdkWdVY62mUgn7aY0c43EhNQEwmF2At5a3SoTEMCpa9PmrWOqP0mX3w2nbuqmX
    iteration-count: 300000
    salt-length: 64
  security:
    access:
      expiration_time: 30000
      secret: MyqdkWdVY62mUgn7aY0c43EhNQEwmF2At5a3SoTEMCpa9PmrWOqP0mX3w2nbuqmX

  # [ALL] front server url
  #front:
  #  server-url : http://**************:38040 //
  #주소 변경 예정
